#!/bin/bash

# Test script for GET Template API by Country Code and Party Code
# This script demonstrates how to test the endpoint with curl commands

BASE_URL="http://localhost:8080"
ENDPOINT="/api/v1/templates"

echo "=== Testing GET Template API by Country Code and Party Code ==="
echo "Base URL: $BASE_URL"
echo "Endpoint: $ENDPOINT/{country_code}/{party_code}"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local country_code=$1
    local party_code=$2
    local description=$3
    local expected_code=$4
    
    echo -e "${BLUE}Testing:${NC} $description"
    echo -e "${YELLOW}Request:${NC} GET $ENDPOINT/$country_code/$party_code"
    
    # Make the request and capture response
    response=$(curl -s -w "\n%{http_code}" "$BASE_URL$ENDPOINT/$country_code/$party_code")
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    echo -e "${YELLOW}Response Code:${NC} $http_code"
    
    # Check if response matches expected
    if [ "$http_code" = "$expected_code" ]; then
        echo -e "${GREEN}✅ PASS${NC}"
    else
        echo -e "${RED}❌ FAIL${NC} (expected: $expected_code)"
    fi
    
    # Pretty print JSON response if successful
    if [ "$http_code" = "200" ]; then
        echo -e "${YELLOW}Response Body:${NC}"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    elif [ "$http_code" -ge "400" ]; then
        echo -e "${YELLOW}Error Response:${NC} $response_body"
    fi
    
    echo ""
}

# Check if server is running
echo "Checking if server is running..."
if curl -s "$BASE_URL/api/v1/templates" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Server is running${NC}"
else
    echo -e "${RED}❌ Server is not running or not accessible${NC}"
    echo "Please start the server with: go run main.go"
    echo "Make sure MongoDB is running and accessible"
    exit 1
fi

echo ""

# Test cases
echo "=== Running Test Cases ==="
echo ""

# Success cases (assuming you have test data)
test_endpoint "US" "ABC123" "Exact case match" "200"
test_endpoint "us" "abc123" "Lowercase case insensitive" "200"
test_endpoint "Us" "AbC123" "Mixed case insensitive" "200"
test_endpoint "uS" "aBc123" "Random case insensitive" "200"

# Error cases
test_endpoint "XX" "NOTFOUND" "Template not found" "404"
test_endpoint "X" "ABC123" "Invalid country code (too short)" "400"
test_endpoint "US" "" "Empty party code" "400"

echo "=== Test Summary ==="
echo ""
echo "The endpoint supports:"
echo "✅ Case insensitive matching for both country and party codes"
echo "✅ Proper HTTP status codes (200, 400, 404, 500)"
echo "✅ Input validation"
echo "✅ JSON response format"
echo ""
echo "=== Sample Data Setup ==="
echo "To test with real data, you can create a template using:"
echo ""
echo "curl -X POST \"$BASE_URL/api/v1/templates/{connection_id}\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"is_default\": false,"
echo "    \"body\": \"Sample receipt body\","
echo "    \"header_text\": {"
echo "      \"font\": \"Arial\","
echo "      \"style\": \"bold\","
echo "      \"color\": \"#000000\","
echo "      \"value\": \"EV Charging Receipt\""
echo "    },"
echo "    \"connection\": {"
echo "      \"connection_name\": \"Test Station\","
echo "      \"country_code\": \"US\","
echo "      \"party_code\": \"ABC123\","
echo "      \"cpo_url\": \"https://example.com\""
echo "    }"
echo "  }'"
echo ""
echo "Then test with: curl \"$BASE_URL$ENDPOINT/US/ABC123\""
