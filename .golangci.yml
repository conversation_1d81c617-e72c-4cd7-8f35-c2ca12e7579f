version: "2"
linters:
  default: none
  enable:
    - bodyclose
    - copyloopvar
    - cyclop
    - decorder
    - dogsled
    - errcheck
    - errname
    - errorlint
    - funlen
    - gocheckcompilerdirectives
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - misspell
    - mnd
    - nakedret
    - noctx
    - nolintlint
    - perfsprint
    - revive
    - staticcheck
    - unconvert
    - unparam
    - unused
    - whitespace
  settings:
    cyclop:
      max-complexity: 30
    funlen:
      lines: -1
      statements: 100
      ignore-comments: true
    gocritic:
      disabled-checks:
        - ifElseChain
        - typeAssertChain
        - yodaStyleExpr
      enabled-tags:
        - diagnostic
        - performance
        - style
      settings:
        hugeParam:
          sizeThreshold: 256
        rangeValCopy:
          sizeThreshold: 256
    gocyclo:
      min-complexity: 50
    gosec:
      excludes:
        - G115
    govet:
      disable:
        - fieldalignment
      enable-all: true
      settings:
        shadow:
          strict: true
    misspell:
      locale: US
    mnd:
      ignored-numbers:
        - "1"
        - "2"
        - "3"
    nolintlint:
      require-explanation: false
      require-specific: false
      allow-unused: false
    revive:
      rules:
        - name: unexported-return
          disabled: true
        - name: unused-parameter
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - gocritic
          - gocyclo
          - gosec
          - staticcheck
        path: _test\.go
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  max-same-issues: 20
formatters:
  enable:
    - gci
    - gofmt
  settings:
    gci:
      sections:
        - standard
        - default
        - prefix(inv-cloud-platform/fe-ev-rcpt-tmpl-api)
    gofmt:
      rewrite-rules:
        - pattern: interface{}
          replacement: any
        - pattern: a[b:len(a)]
          replacement: a[b:]
    goimports:
      local-prefixes:
        - inv-cloud-platform
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
