// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	strictnethttp "github.com/oapi-codegen/runtime/strictmiddleware/nethttp"
)

// Body defines model for Body.
type Body = string

// Connection defines model for Connection.
type Connection struct {
	ConnectionName string `json:"connection_name,omitempty"`
	CountryCode    string `json:"country_code,omitempty"`
	CpoUrl         string `json:"cpo_url,omitempty"`
	PartyCode      string `json:"party_code,omitempty"`
}

// ErrorResponse defines model for ErrorResponse.
type ErrorResponse struct {
	StatusCode *int `json:"status_code,omitempty"`

	// StatusMessage A description of the error.
	StatusMessage *string `json:"status_message,omitempty"`

	// Timestamp The time this message was generated.
	Timestamp *time.Time `json:"timestamp,omitempty"`
}

// Logo defines model for Logo.
type Logo struct {
	Height int    `json:"height,omitempty"`
	Src    string `json:"src,omitempty"`
	Width  int    `json:"width,omitempty"`
}

// TemplateResponse defines model for TemplateResponse.
type TemplateResponse struct {
	Body         Body        `json:"body,omitempty"`
	Connection   *Connection `json:"connection,omitempty"`
	ConnectionId string      `json:"connection_id,omitempty"`
	FooterLogo   *Logo       `json:"footer_logo,omitempty"`
	FooterText   *Text       `json:"footer_text,omitempty"`
	HeaderLogo   *Logo       `json:"header_logo,omitempty"`
	HeaderText   *Text       `json:"header_text,omitempty"`
	IsDefault    bool        `json:"is_default,omitempty"`
	LastUpdated  *time.Time  `json:"last_updated,omitempty"`
	TemplateId   string      `json:"template_id,omitempty"`
}

// TemplatesResponse defines model for TemplatesResponse.
type TemplatesResponse = []TemplateResponse

// Text defines model for Text.
type Text struct {
	Color string `json:"color,omitempty"`
	Font  string `json:"font,omitempty"`
	Style string `json:"style,omitempty"`
	Value string `json:"value,omitempty"`
}

// ConnectionID Unique connection id of party for which service is requesting receipt template.
type ConnectionID = string

// CountryCode ISO 3166-1 alpha-2 country code
type CountryCode = string

// PartyCode defines model for PartyCode.
type PartyCode = string

// TemplateID Unique id of the new or updated receipt template object.
type TemplateID = string

// NewTemplateJSONRequestBody defines body for NewTemplate for application/json ContentType.
type NewTemplateJSONRequestBody = TemplateResponse

// ModifyTemplateJSONRequestBody defines body for ModifyTemplate for application/json ContentType.
type ModifyTemplateJSONRequestBody = TemplateResponse

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Retrieve All Receipts Template Information
	// (GET /api/v1/templates)
	GetTemplates(w http.ResponseWriter, r *http.Request)
	// Add new template
	// (POST /api/v1/templates/{connection_id})
	NewTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID)
	// Delete existing template
	// (DELETE /api/v1/templates/{connection_id}/{template_id})
	DeleteTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID)
	// Retrieve Receipt Template Information
	// (GET /api/v1/templates/{connection_id}/{template_id})
	GetTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID)
	// Update existing template
	// (PUT /api/v1/templates/{connection_id}/{template_id})
	ModifyTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID)
	// Retrieve Receipt Template Information by Country and Party Code
	// (GET /api/v1/templates/{country_code}/{party_code})
	GetTemplateByCountryAndParty(w http.ResponseWriter, r *http.Request, countryCode CountryCode, partyCode PartyCode)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Retrieve All Receipts Template Information
// (GET /api/v1/templates)
func (_ Unimplemented) GetTemplates(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Add new template
// (POST /api/v1/templates/{connection_id})
func (_ Unimplemented) NewTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Delete existing template
// (DELETE /api/v1/templates/{connection_id}/{template_id})
func (_ Unimplemented) DeleteTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Retrieve Receipt Template Information
// (GET /api/v1/templates/{connection_id}/{template_id})
func (_ Unimplemented) GetTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update existing template
// (PUT /api/v1/templates/{connection_id}/{template_id})
func (_ Unimplemented) ModifyTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Retrieve Receipt Template Information by Country and Party Code
// (GET /api/v1/templates/{country_code}/{party_code})
func (_ Unimplemented) GetTemplateByCountryAndParty(w http.ResponseWriter, r *http.Request, countryCode CountryCode, partyCode PartyCode) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetTemplates operation middleware
func (siw *ServerInterfaceWrapper) GetTemplates(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTemplates(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// NewTemplate operation middleware
func (siw *ServerInterfaceWrapper) NewTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", chi.URLParam(r, "connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.NewTemplate(w, r, connectionId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DeleteTemplate operation middleware
func (siw *ServerInterfaceWrapper) DeleteTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", chi.URLParam(r, "connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	// ------------- Path parameter "template_id" -------------
	var templateId TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "template_id", chi.URLParam(r, "template_id"), &templateId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "template_id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteTemplate(w, r, connectionId, templateId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTemplate operation middleware
func (siw *ServerInterfaceWrapper) GetTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", chi.URLParam(r, "connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	// ------------- Path parameter "template_id" -------------
	var templateId TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "template_id", chi.URLParam(r, "template_id"), &templateId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "template_id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTemplate(w, r, connectionId, templateId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ModifyTemplate operation middleware
func (siw *ServerInterfaceWrapper) ModifyTemplate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "connection_id" -------------
	var connectionId ConnectionID

	err = runtime.BindStyledParameterWithOptions("simple", "connection_id", chi.URLParam(r, "connection_id"), &connectionId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "connection_id", Err: err})
		return
	}

	// ------------- Path parameter "template_id" -------------
	var templateId TemplateID

	err = runtime.BindStyledParameterWithOptions("simple", "template_id", chi.URLParam(r, "template_id"), &templateId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "template_id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ModifyTemplate(w, r, connectionId, templateId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTemplateByCountryAndParty operation middleware
func (siw *ServerInterfaceWrapper) GetTemplateByCountryAndParty(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "country_code" -------------
	var countryCode CountryCode

	err = runtime.BindStyledParameterWithOptions("simple", "country_code", chi.URLParam(r, "country_code"), &countryCode, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "country_code", Err: err})
		return
	}

	// ------------- Path parameter "party_code" -------------
	var partyCode PartyCode

	err = runtime.BindStyledParameterWithOptions("simple", "party_code", chi.URLParam(r, "party_code"), &partyCode, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "party_code", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTemplateByCountryAndParty(w, r, countryCode, partyCode)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/api/v1/templates", wrapper.GetTemplates)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/api/v1/templates/{connection_id}", wrapper.NewTemplate)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/api/v1/templates/{connection_id}/{template_id}", wrapper.DeleteTemplate)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/api/v1/templates/{connection_id}/{template_id}", wrapper.GetTemplate)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/api/v1/templates/{connection_id}/{template_id}", wrapper.ModifyTemplate)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/api/v1/templates/{country_code}/{party_code}", wrapper.GetTemplateByCountryAndParty)
	})

	return r
}

type GetTemplatesRequestObject struct {
}

type GetTemplatesResponseObject interface {
	VisitGetTemplatesResponse(w http.ResponseWriter) error
}

type GetTemplates200JSONResponse TemplatesResponse

func (response GetTemplates200JSONResponse) VisitGetTemplatesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplatesdefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response GetTemplatesdefaultJSONResponse) VisitGetTemplatesResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type NewTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	Body         *NewTemplateJSONRequestBody
}

type NewTemplateResponseObject interface {
	VisitNewTemplateResponse(w http.ResponseWriter) error
}

type NewTemplate201Response struct {
}

func (response NewTemplate201Response) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(201)
	return nil
}

type NewTemplate400Response struct {
}

func (response NewTemplate400Response) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(400)
	return nil
}

type NewTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response NewTemplatedefaultJSONResponse) VisitNewTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type DeleteTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	TemplateId   TemplateID   `json:"template_id"`
}

type DeleteTemplateResponseObject interface {
	VisitDeleteTemplateResponse(w http.ResponseWriter) error
}

type DeleteTemplate204Response struct {
}

func (response DeleteTemplate204Response) VisitDeleteTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type DeleteTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response DeleteTemplatedefaultJSONResponse) VisitDeleteTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type GetTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	TemplateId   TemplateID   `json:"template_id"`
}

type GetTemplateResponseObject interface {
	VisitGetTemplateResponse(w http.ResponseWriter) error
}

type GetTemplate200JSONResponse TemplateResponse

func (response GetTemplate200JSONResponse) VisitGetTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response GetTemplatedefaultJSONResponse) VisitGetTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type ModifyTemplateRequestObject struct {
	ConnectionId ConnectionID `json:"connection_id"`
	TemplateId   TemplateID   `json:"template_id"`
	Body         *ModifyTemplateJSONRequestBody
}

type ModifyTemplateResponseObject interface {
	VisitModifyTemplateResponse(w http.ResponseWriter) error
}

type ModifyTemplate204Response struct {
}

func (response ModifyTemplate204Response) VisitModifyTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(204)
	return nil
}

type ModifyTemplate400Response struct {
}

func (response ModifyTemplate400Response) VisitModifyTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(400)
	return nil
}

type ModifyTemplate409Response struct {
}

func (response ModifyTemplate409Response) VisitModifyTemplateResponse(w http.ResponseWriter) error {
	w.WriteHeader(409)
	return nil
}

type ModifyTemplatedefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response ModifyTemplatedefaultJSONResponse) VisitModifyTemplateResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

type GetTemplateByCountryAndPartyRequestObject struct {
	CountryCode CountryCode `json:"country_code"`
	PartyCode   PartyCode   `json:"party_code"`
}

type GetTemplateByCountryAndPartyResponseObject interface {
	VisitGetTemplateByCountryAndPartyResponse(w http.ResponseWriter) error
}

type GetTemplateByCountryAndParty200JSONResponse TemplateResponse

func (response GetTemplateByCountryAndParty200JSONResponse) VisitGetTemplateByCountryAndPartyResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(200)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplateByCountryAndParty404JSONResponse ErrorResponse

func (response GetTemplateByCountryAndParty404JSONResponse) VisitGetTemplateByCountryAndPartyResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(404)

	return json.NewEncoder(w).Encode(response)
}

type GetTemplateByCountryAndPartydefaultJSONResponse struct {
	Body       ErrorResponse
	StatusCode int
}

func (response GetTemplateByCountryAndPartydefaultJSONResponse) VisitGetTemplateByCountryAndPartyResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(response.StatusCode)

	return json.NewEncoder(w).Encode(response.Body)
}

// StrictServerInterface represents all server handlers.
type StrictServerInterface interface {
	// Retrieve All Receipts Template Information
	// (GET /api/v1/templates)
	GetTemplates(ctx context.Context, request GetTemplatesRequestObject) (GetTemplatesResponseObject, error)
	// Add new template
	// (POST /api/v1/templates/{connection_id})
	NewTemplate(ctx context.Context, request NewTemplateRequestObject) (NewTemplateResponseObject, error)
	// Delete existing template
	// (DELETE /api/v1/templates/{connection_id}/{template_id})
	DeleteTemplate(ctx context.Context, request DeleteTemplateRequestObject) (DeleteTemplateResponseObject, error)
	// Retrieve Receipt Template Information
	// (GET /api/v1/templates/{connection_id}/{template_id})
	GetTemplate(ctx context.Context, request GetTemplateRequestObject) (GetTemplateResponseObject, error)
	// Update existing template
	// (PUT /api/v1/templates/{connection_id}/{template_id})
	ModifyTemplate(ctx context.Context, request ModifyTemplateRequestObject) (ModifyTemplateResponseObject, error)
	// Retrieve Receipt Template Information by Country and Party Code
	// (GET /api/v1/templates/{country_code}/{party_code})
	GetTemplateByCountryAndParty(ctx context.Context, request GetTemplateByCountryAndPartyRequestObject) (GetTemplateByCountryAndPartyResponseObject, error)
}

type StrictHandlerFunc = strictnethttp.StrictHTTPHandlerFunc
type StrictMiddlewareFunc = strictnethttp.StrictHTTPMiddlewareFunc

type StrictHTTPServerOptions struct {
	RequestErrorHandlerFunc  func(w http.ResponseWriter, r *http.Request, err error)
	ResponseErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

func NewStrictHandler(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: StrictHTTPServerOptions{
		RequestErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		},
		ResponseErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		},
	}}
}

func NewStrictHandlerWithOptions(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc, options StrictHTTPServerOptions) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: options}
}

type strictHandler struct {
	ssi         StrictServerInterface
	middlewares []StrictMiddlewareFunc
	options     StrictHTTPServerOptions
}

// GetTemplates operation middleware
func (sh *strictHandler) GetTemplates(w http.ResponseWriter, r *http.Request) {
	var request GetTemplatesRequestObject

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetTemplates(ctx, request.(GetTemplatesRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetTemplates")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetTemplatesResponseObject); ok {
		if err := validResponse.VisitGetTemplatesResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// NewTemplate operation middleware
func (sh *strictHandler) NewTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID) {
	var request NewTemplateRequestObject

	request.ConnectionId = connectionId

	var body NewTemplateJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.NewTemplate(ctx, request.(NewTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "NewTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(NewTemplateResponseObject); ok {
		if err := validResponse.VisitNewTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// DeleteTemplate operation middleware
func (sh *strictHandler) DeleteTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	var request DeleteTemplateRequestObject

	request.ConnectionId = connectionId
	request.TemplateId = templateId

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.DeleteTemplate(ctx, request.(DeleteTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "DeleteTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(DeleteTemplateResponseObject); ok {
		if err := validResponse.VisitDeleteTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetTemplate operation middleware
func (sh *strictHandler) GetTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	var request GetTemplateRequestObject

	request.ConnectionId = connectionId
	request.TemplateId = templateId

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetTemplate(ctx, request.(GetTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetTemplateResponseObject); ok {
		if err := validResponse.VisitGetTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// ModifyTemplate operation middleware
func (sh *strictHandler) ModifyTemplate(w http.ResponseWriter, r *http.Request, connectionId ConnectionID, templateId TemplateID) {
	var request ModifyTemplateRequestObject

	request.ConnectionId = connectionId
	request.TemplateId = templateId

	var body ModifyTemplateJSONRequestBody
	if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
		sh.options.RequestErrorHandlerFunc(w, r, fmt.Errorf("can't decode JSON body: %w", err))
		return
	}
	request.Body = &body

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.ModifyTemplate(ctx, request.(ModifyTemplateRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "ModifyTemplate")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(ModifyTemplateResponseObject); ok {
		if err := validResponse.VisitModifyTemplateResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// GetTemplateByCountryAndParty operation middleware
func (sh *strictHandler) GetTemplateByCountryAndParty(w http.ResponseWriter, r *http.Request, countryCode CountryCode, partyCode PartyCode) {
	var request GetTemplateByCountryAndPartyRequestObject

	request.CountryCode = countryCode
	request.PartyCode = partyCode

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetTemplateByCountryAndParty(ctx, request.(GetTemplateByCountryAndPartyRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetTemplateByCountryAndParty")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetTemplateByCountryAndPartyResponseObject); ok {
		if err := validResponse.VisitGetTemplateByCountryAndPartyResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xZ32/bOBL+Vwa8e5T8K0GB+i1Ne4cA7V2QTfdh2yKgxZHFLkWqJGXHCPS/L0hKlhQ5",
	"sZO4W3R3n1o5w+Hwm2/mG0p3JFF5oSRKa8j8jhRU0xwtav90rqTExHIlL966Zy7JnBTUZiQikuZI5iTZ",
	"mtxwRiKi8VvJNTIyt7rEiJgkw5y6xQxNonnhTMmcfJT8W4nQLgfOQKVQUG03kCoN64wnGRjUK54gcAPO",
	"NRrL5RI0JsgLCxbzQlCLIxKRnN6+R7m0GZmfvIpIzmX30W4KF66xmsslqaqInKtSWr05VwwfPJu3uEmc",
	"yeFHu/jl/3AyffUqngIVRUbjGdSeoPbUiW22K7RLB8IjgXmQ9ofV2Wa6a5vrGr0Hk9vA+6zUhnzaDEHi",
	"GpSGsmDUIhskD9TiKyb26Tmsmhg8Wd8otnH/9q0ichsvVex+jM3vvIiVD5OKuFBcWtThNJ4PDRV9IWhV",
	"oLYcve8OywM0z9+mR6qX+CnUTanFS1x0aPRsL9U2LSGLpIrIO62VvkJTKGlwCKax1Jbm/r7O6RK1W18b",
	"5GgMXeKQYGfQeW5Ihm5TxyG8pXkhnMszCaXE2wITRztvACpJSq2ROct7fIqI5TkaS/NiuOd1huD+DDbj",
	"BurQYE0NLFGidsTubz6bzE7i6SSevb6ezuaTyXwy+Y1EJFU6p5bMiauF2Hkku0pzgOl7tVRDKDPky8zu",
	"QPFwEhidvIRDa85ceT47gF1nbfrSwxRa1LX+b40pmZN/jVsVG9ctYez7gS+4bl0/tqLTAXrrXPtz7fQJ",
	"zelwAFOlLOobUef3sfg8B9olFm/tviXXzqaKSIaUPXGXeslTduHmhmFKS2FDAdX/TakwuEVooZRAKp8A",
	"kaDG3tTq4RwfUkNRT7y+U/YeI6/pspdbzM1+FO/RvnVPtaab4D1k4748CaVfUsWpkvYl643diBep2YqK",
	"8qgq5H7iMvVst9z6lvyfd/G7X+Or88vr+PrD5fv47PKCRGSF2oQePx1NRhOHsipQ0oKTOTkZTUYzEvm5",
	"yEM9pgUfr6bjhl3+xyXaXYrBDaBkPlLQaDXHFRpgaCkXyMCF54jsRIwuVGmBCjGYjgzQFeWCLgSOPkvv",
	"tLuQy0SUDA240o4gFG0EoUUA2mT02ZWaY4tfcMHInPwX7ZamfrALhPNnmU0m9cRjMZCCFoXgiV89/mpC",
	"G21nv0MY3daCz0sfp+0Y2D1WijbJkAGVbl60pZZgyiRBY9JSCF8KnU5zlGD7U8uOQL0BNGC5q4ulXLrr",
	"iJs/wtDiJ3wfdRg3mhnGz6plnlO9IXNyVZMBzoSAq5BwAw1acNEC4RobXRoy/0SoEHGDVdzBinxxzge8",
	"HN/1BKzyXUOZvURNNLoYqJ/at7lxJzJ0hcAtcGkVMGrpghrcwa7/4bo5iq+c9kb5aXcGWpNx78ZZfQmX",
	"DjS2Ge6PSstusqtBGUyHSPUgCUCxAS9PQwH1Vy4oa+6vPzl3zxjrUaPDUInrLUMPZOX4riPTVcBNoMV9",
	"LF1zISCYAt7y8FagcbWDk2+96ZFoGe2179ysA4l7zDodnm5whvpw7K/U9t4+kK8OhcKpuyyKjqeuYApM",
	"eMqT4SuIxQaWfIWy/YUH8em9oHL6i0eV3x/MxMl37an/KH2r9LXK7xP5BwQ+IkW5twjCDWlYXvcI+7h4",
	"f1CMp5sfR88fpfYH9eTmDeZzJP908npokyiZCp50GhEVGinbhCzCmtsMDM17OfzJS+LjAzTtVEHA+ZBZ",
	"on2XWo3v2veZ1VFvZYfoRvcNv4cgfMhwj6MgG49YQE5tkjkwuIGEGpdug9Jwy1fN8mOqzptN/fHjTDL/",
	"reEZdd5+PDmgzNsPGj+tCJ2GFvHnVNs2SKkspKqUzH8Sc6W2l25/B8V0dVdT0Dv0/ILz8EFsj5ZWVfVH",
	"AAAA//8mz58f9hwAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
