# GET Template API by Country Code and Party Code

## Overview

The new API endpoint allows retrieving receipt templates by country code and party code with case-insensitive matching.

## Endpoint

```
GET /api/v1/templates/{country_code}/{party_code}
```

## Parameters

- `country_code` (path parameter): ISO 3166-1 alpha-2 country code (minimum 2 characters)
- `party_code` (path parameter): Party code (minimum 1 character)

## Features

- **Case Insensitive**: Both country code and party code matching is case insensitive
- **Exact Match**: Uses regex pattern matching for precise results
- **Error Handling**: Returns appropriate HTTP status codes for different scenarios

## Examples

### Successful Request (Case Sensitive)
```bash
curl -X GET "http://localhost:8080/api/v1/templates/US/ABC123"
```

### Successful Request (Case Insensitive)
```bash
curl -X GET "http://localhost:8080/api/v1/templates/us/abc123"
curl -X GET "http://localhost:8080/api/v1/templates/Us/AbC123"
```

### Response (200 OK)
```json
{
  "template_id": "550e8400-e29b-41d4-a716-446655440000",
  "connection_id": "550e8400-e29b-41d4-a716-446655440001",
  "is_default": false,
  "header_text": {
    "font": "arial",
    "style": "bold",
    "color": "#000000",
    "value": "Receipt Header"
  },
  "header_logo": {
    "src": "https://example.com/logo.png",
    "width": 200,
    "height": 100
  },
  "body": "Receipt body content",
  "footer_text": {
    "font": "arial",
    "style": "normal",
    "color": "#666666",
    "value": "Thank you for your business"
  },
  "footer_logo": {
    "src": "https://example.com/footer-logo.png",
    "width": 150,
    "height": 50
  },
  "connection": {
    "connection_name": "Test Connection",
    "country_code": "US",
    "party_code": "ABC123",
    "cpo_url": "https://example.com"
  },
  "last_updated": "2023-10-29T12:00:00Z"
}
```

## Error Responses

### Template Not Found (404)
```bash
curl -X GET "http://localhost:8080/api/v1/templates/XX/NOTFOUND"
```

Response:
```json
{
  "status_code": 404,
  "status_message": "template not found",
  "timestamp": "2023-10-29T12:00:00Z"
}
```

### Invalid Country Code (400)
```bash
curl -X GET "http://localhost:8080/api/v1/templates/X/ABC123"
```

Response:
```json
{
  "status_code": 400,
  "status_message": "invalid country code",
  "timestamp": "2023-10-29T12:00:00Z"
}
```

### Empty Party Code (400)
```bash
curl -X GET "http://localhost:8080/api/v1/templates/US/"
```

Response:
```json
{
  "status_code": 400,
  "status_message": "invalid party code",
  "timestamp": "2023-10-29T12:00:00Z"
}
```

## Implementation Details

### Database Query
The implementation uses MongoDB with case-insensitive regex matching:

```javascript
{
  "connection.country_code": { "$regex": "^US$", "$options": "i" },
  "connection.party_code": { "$regex": "^ABC123$", "$options": "i" }
}
```

### Validation
- Country code must be at least 2 characters (as per ISO 3166-1 alpha-2 standard)
- Party code must be at least 1 character
- Both parameters are required

### Case Insensitivity
The following combinations will all match the same template:
- `US/ABC123`
- `us/abc123`
- `Us/AbC123`
- `uS/aBc123`
