package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/inv-cloud-platform/hub-com-tools-go/hubmongo"
	"github.com/rs/zerolog/log"

	mongoadapter "inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/mongo"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/adapter/resthttp"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/config"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/service"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/server"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/openapi"
)

func main() {
	log.Info().Msg("Application starting ...")

	ctx := context.Background()
	cfg := config.Load()
	config.SetupLogger(cfg.App.LogLevel)

	dbClient, err := hubmongo.ConnectV2(&hubmongo.Config{
		Schema:     cfg.MongoDB.Schema,
		Host:       cfg.MongoDB.Host,
		Port:       cfg.MongoDB.Port,
		Username:   cfg.MongoDB.Username,
		Password:   cfg.MongoDB.Password,
		AuthSource: cfg.MongoDB.AuthSource,
		Options:    cfg.MongoDB.Options,
		Timeout:    cfg.MongoDB.Timeout,
	})
	if err != nil {
		log.Error().Err(err).Msg("unable to connect to MongoDB")
		return
	}

	collection := dbClient.Database(cfg.MongoDB.Database).Collection(cfg.MongoDB.TemplatesCollection)

	templateRepo := mongoadapter.NewTemplateRepository(collection)
	templateSrv := service.NewTemplateSrv(templateRepo)
	apiHandler := resthttp.NewHandler(templateSrv)
	srv := server.New(cfg.App, openapi.Handler(apiHandler))
	go srv.Start(cfg.App.ServerPort)

	// close the server and all connections created
	defer func() {
		if err = srv.Shutdown(); err != nil {
			log.Error().Err(err).Msg("failed to shutdown server")
		}

		if err = dbClient.Disconnect(ctx); err != nil {
			log.Error().Err(err).Msg("failed to close mongo connection")
		}
	}()

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	<-ctx.Done()

	log.Info().Msg("Application stopping ...")
}
