package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/config"
	custmiddleware "inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/server/middleware"
)

type Server struct {
	Srv *http.Server
}

// New create a http server, set up the middlewares and paths, and return a new instance of it
func New(cfg *config.App, handler http.Handler, apiMiddlewares ...func(http.Handler) http.Handler) *Server {
	router := chi.NewRouter()

	setupMiddlewares(router)

	// sets up the metrics endpoint used in prometheus and for monitoring
	router.Mount("/metrics", promhttp.Handler())

	// mount all the extras middlewares and assign the given handler to them
	router.With(apiMiddlewares...).Mount("/", handler)

	server := &http.Server{
		Handler:     router,
		ReadTimeout: cfg.ReadTimeout,
	}

	return &Server{
		Srv: server,
	}
}

// setupMiddlewares add default middlewares to the router
func setupMiddlewares(r *chi.Mux) {
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(middleware.Recoverer)
	r.Use(middleware.CleanPath)
	r.Use(custmiddleware.RequestLogger)
	r.Use(custmiddleware.PrometheusMiddleware)

	compressionLevel := 5
	r.Use(middleware.Compress(compressionLevel))

	// sets up the health-check endpoint
	r.Use(middleware.Heartbeat("/health"))
}

// Start the port on the designed port
func (s *Server) Start(port int) {
	s.Srv.Addr = fmt.Sprintf(":%d", port)
	log.Info().Msgf("starting server on port :%d", port)

	if err := s.Srv.ListenAndServe(); err != nil {
		if errors.Is(err, http.ErrServerClosed) {
			log.Info().Msg("server closed")
		}
		log.Fatal().Err(err).Msg("failed to start server")
	}
}

// Shutdown the server
func (s *Server) Shutdown() error {
	log.Info().Msg("shutting down server")

	if s.Srv != nil {
		ctx, cancel := context.WithTimeout(context.Background(), s.Srv.ReadTimeout)
		defer cancel()
		return s.Srv.Shutdown(ctx)
	}

	return nil
}
