package middleware

import (
	"net/http"

	"github.com/go-chi/chi/v5/middleware"
)

func RequestLogger(next http.Handler) http.Handler {
	return http.HandlerFunc(
		func(w http.ResponseWriter, r *http.Request) {
			// skip logging for those requests
			if r.URL.Path == "/metrics" || r.URL.Path == "/health" {
				next.ServeHTTP(w, r)
				return
			}
			// for all other requests, proceed with logging
			middleware.Logger(next).ServeHTTP(w, r)
		})
}
