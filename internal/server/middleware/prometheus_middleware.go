package middleware

import (
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func newResponseWriter(w http.ResponseWriter) *responseWriter {
	return &responseWriter{w, http.StatusOK}
}
func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

var (
	HTTPDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "fe_ev_rcpt_tmpl_api_http_request_duration_seconds",
			Help:    "Duration of HTTP requests",
			Buckets: []float64{.010, .050, .100, .150, .200, .300, 0.500, 1, 5},
		}, []string{"path"})
	RequestCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "fe_ev_rcpt_tmpl_api_http_request_total",
			Help: "The number of requests",
		}, []string{"path", "status"})
	ErrorCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "fe_ev_rcpt_tmpl_api_errors_total",
			Help: "Total number of errors",
		}, []string{"path", "errorCode"})
)

// PrometheusMiddleware is used for monitoring the api
func PrometheusMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		rw := newResponseWriter(w)
		begin := time.Now()
		next.ServeHTTP(rw, r)
		elapsed := time.Since(begin)
		path := chi.RouteContext(r.Context()).RoutePattern()
		HTTPDuration.WithLabelValues(path).Observe(elapsed.Seconds())
		RequestCounter.WithLabelValues(path, strconv.Itoa(rw.statusCode)).Inc()
	})
}
