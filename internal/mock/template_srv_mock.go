package mock

import (
	"context"

	"github.com/stretchr/testify/mock"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
)

type TemplateServiceMock struct {
	mock.Mock
}

func (m *TemplateServiceMock) ListTemplate(ctx context.Context) (domain.Templates, error) {
	args := m.Called(ctx)
	if args.Get(0) != nil {
		return args.Get(0).(domain.Templates), args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *TemplateServiceMock) RetrieveTemplate(ctx context.Context, templateID, connectionID string) (*domain.Template, error) {
	args := m.Called(ctx, templateID, connectionID)
	if args.Get(0) != nil {
		return args.Get(0).(*domain.Template), args.Error(1)
	}
	return nil, args.Error(1)
}

func (m *TemplateServiceMock) NewTemplate(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)
	if args.Get(0) != nil {
		return args.Error(0)
	}
	return nil
}

func (m *TemplateServiceMock) ModifyTemplate(ctx context.Context, template *domain.Template) error {
	args := m.Called(ctx, template)
	if args.Get(0) != nil {
		return args.Error(0)
	}
	return nil
}

func (m *TemplateServiceMock) RemoveTemplate(ctx context.Context, templateID, connectionID string) error {
	args := m.Called(ctx, templateID, connectionID)
	if args.Get(0) != nil {
		return args.Error(0)
	}
	return nil
}
