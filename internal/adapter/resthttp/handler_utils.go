package resthttp

import (
	"encoding/json"
	"net/http"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/openapi"
)

func Success(w http.ResponseWriter, data any, status int) {
	response(w, data, status)
}

func response(w http.ResponseWriter, data any, httpStatus int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(httpStatus)

	if err := json.NewEncoder(w).Encode(&data); err != nil {
		http.Error(w, "internal server error", http.StatusInternalServerError)
		return
	}
}

func ToOpenAPIModel(template *domain.Template) *openapi.TemplateResponse {
	return &openapi.TemplateResponse{
		TemplateId:   template.TemplateID,
		ConnectionId: template.ConnectionID,
		IsDefault:    template.IsDefault,
		Body:         template.Body,
		HeaderLogo: &openapi.Logo{
			Src:    template.HeaderLogo.Src,
			Height: template.HeaderLogo.Height,
			Width:  template.HeaderLogo.Width,
		},
		HeaderText: &openapi.Text{
			Color: template.HeaderText.Color,
			Font:  template.HeaderText.Font,
			Style: template.HeaderText.Style,
			Value: template.HeaderText.Value,
		},
		FooterLogo: &openapi.Logo{
			Src:    template.FooterLogo.Src,
			Height: template.FooterLogo.Height,
			Width:  template.FooterLogo.Width,
		},
		FooterText: &openapi.Text{
			Color: template.FooterText.Color,
			Font:  template.FooterText.Font,
			Style: template.FooterText.Style,
			Value: template.FooterText.Value,
		},

		Connection: &openapi.Connection{
			ConnectionName: template.Connection.ConnectionName,
			CountryCode:    template.Connection.CountryCode,
			PartyCode:      template.Connection.PartyCode,
			CpoUrl:         template.Connection.CPOURL,
		},

		LastUpdated: &template.LastUpdated,
	}
}

func ToOpenAPIListModel(templates domain.Templates) openapi.TemplatesResponse {
	var apiTemplates openapi.TemplatesResponse
	for _, tmpl := range templates {
		apiTemplate := openapi.TemplateResponse{
			TemplateId:   tmpl.TemplateID,
			ConnectionId: tmpl.ConnectionID,
			Body:         tmpl.Body,
			IsDefault:    tmpl.IsDefault,
			FooterLogo: &openapi.Logo{
				Height: tmpl.FooterLogo.Height,
				Width:  tmpl.FooterLogo.Width,
				Src:    tmpl.FooterLogo.Src,
			},
			FooterText: &openapi.Text{
				Color: tmpl.FooterText.Color,
				Font:  tmpl.FooterText.Font,
				Style: tmpl.FooterText.Style,
				Value: tmpl.FooterText.Value,
			},
			HeaderLogo: &openapi.Logo{
				Height: tmpl.HeaderLogo.Height,
				Width:  tmpl.HeaderLogo.Width,
				Src:    tmpl.HeaderLogo.Src,
			},
			HeaderText: &openapi.Text{
				Color: tmpl.HeaderText.Color,
				Font:  tmpl.HeaderText.Font,
				Style: tmpl.HeaderText.Style,
				Value: tmpl.HeaderText.Value,
			},
			Connection: &openapi.Connection{
				ConnectionName: tmpl.Connection.ConnectionName,
				CountryCode:    tmpl.Connection.CountryCode,
				PartyCode:      tmpl.Connection.PartyCode,
				CpoUrl:         tmpl.Connection.CPOURL,
			},
			LastUpdated: &tmpl.LastUpdated,
		}

		apiTemplates = append(apiTemplates, apiTemplate)
	}
	return apiTemplates
}
