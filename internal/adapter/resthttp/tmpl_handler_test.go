package resthttp

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
	apimock "inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/mock"
)

func TestListTemplates(t *testing.T) {
	mockTemplateSrv := new(apimock.TemplateServiceMock)
	h := NewHandler(mockTemplateSrv)

	templateID := uuid.NewString()
	connectionID := uuid.NewString()
	mockTemplates := domain.Templates{
		{
			TemplateID:   templateID,
			ConnectionID: connectionID,
			IsDefault:    true,
			Body:         "test-body",
		},
	}

	tests := []struct {
		name               string
		mockError          error
		expectedStatusCode int
	}{
		{
			name:               "retrieve all templates successfully",
			mockError:          nil,
			expectedStatusCode: http.StatusOK,
		},
		{
			name:               "all template retrieve error",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockError != nil {
				mockTemplateSrv.On("ListTemplate", context.Background()).
					Return(nil, tt.mockError).Once()
			} else {
				mockTemplateSrv.On("ListTemplate", context.Background()).
					Return(mockTemplates, nil).Once()
			}

			req, err := http.NewRequestWithContext(context.Background(), http.MethodGet, "/api/v1/templates", http.NoBody)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				h.GetTemplates(w, r)
			})
			handlerFunc.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatusCode, rr.Code)
			mockTemplateSrv.AssertExpectations(t)
		})
	}
}

func TestGetTemplate(t *testing.T) {
	mockTemplateSrv := new(apimock.TemplateServiceMock)
	h := NewHandler(mockTemplateSrv)

	templateID := uuid.NewString()
	connectionID := uuid.NewString()
	mockTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
	}

	tests := []struct {
		name               string
		templateID         string
		connectionID       string
		mockError          error
		expectedStatusCode int
	}{
		{
			name:               "retrieve template successfully",
			templateID:         "0195a7ae-b326-74df-8112-153e45ac0760",
			connectionID:       "0195a7ae-b326-7b6f-88a6-d6102e5abc9a",
			mockError:          nil,
			expectedStatusCode: http.StatusOK,
		},
		{
			name:               "missing template id",
			templateID:         "",
			connectionID:       "0195a7ae-b326-7b6f-88a6-d6102e5abc9a",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "missing connection id",
			templateID:         "0195a7ae-b326-74df-8112-153e45ac0760",
			connectionID:       "",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "template retrieve error",
			templateID:         "0195a7ae-b326-74df-8112-153e45ac0760",
			connectionID:       "0195a7ae-b326-7b6f-88a6-d6102e5abc9a",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockError != nil && tt.templateID != "" && tt.connectionID != "" {
				mockTemplateSrv.On("RetrieveTemplate", context.Background(), tt.templateID, tt.connectionID).
					Return(nil, tt.mockError).Once()
			}

			if tt.mockError == nil {
				mockTemplateSrv.On("RetrieveTemplate", context.Background(), tt.templateID, tt.connectionID).
					Return(mockTemplate, nil).Once()
			}

			req, err := http.NewRequestWithContext(context.Background(), http.MethodGet, fmt.Sprintf("/api/v1/templates/%s/%s", tt.templateID, tt.connectionID), http.NoBody)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				h.GetTemplate(w, r, tt.templateID, tt.connectionID)
			})
			handlerFunc.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatusCode, rr.Code)
			mockTemplateSrv.AssertExpectations(t)
		})
	}
}

func TestNewTemplate(t *testing.T) {
	mockTemplateSrv := new(apimock.TemplateServiceMock)
	h := NewHandler(mockTemplateSrv)

	connectioID := uuid.NewString()

	mockTemplate := &domain.Template{
		ConnectionID: connectioID,
		IsDefault:    true,
		Body:         "test-body",
	}

	body, err := json.Marshal(mockTemplate)
	if err != nil {
		t.Fatal("template marshal error")
	}

	tests := []struct {
		name               string
		connectionID       string
		mockError          error
		expectedStatusCode int
	}{
		{
			name:               "invalid connection id",
			connectionID:       "",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "template saving error",
			connectionID:       connectioID,
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusInternalServerError,
		},
		{
			name:               "new template creation successful",
			connectionID:       connectioID,
			mockError:          nil,
			expectedStatusCode: http.StatusCreated,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockError != nil && tt.connectionID != "" {
				mockTemplateSrv.On("NewTemplate", context.Background(), mockTemplate).
					Return(tt.mockError).Once()
			}

			if tt.mockError == nil {
				mockTemplateSrv.On("NewTemplate", context.Background(), mockTemplate).
					Return(nil).Once()
			}

			var req *http.Request
			req, err = http.NewRequestWithContext(context.Background(), http.MethodPost, "/api/v1/templates/"+tt.connectionID, bytes.NewReader(body))
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				h.NewTemplate(w, r, tt.connectionID)
			})
			handlerFunc.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatusCode, rr.Code)
			mockTemplateSrv.AssertExpectations(t)
		})
	}
}

func TestUpdateTemplate(t *testing.T) {
	mockTemplateSrv := new(apimock.TemplateServiceMock)
	h := NewHandler(mockTemplateSrv)

	templateID := uuid.NewString()
	connectionID := uuid.NewString()
	mockTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
	}

	body, err := json.Marshal(mockTemplate)
	if err != nil {
		t.Fatal("marshal error")
	}

	tests := []struct {
		name               string
		templateID         string
		connectionID       string
		mockError          error
		expectedStatusCode int
	}{
		{
			name:               "update template successful",
			templateID:         templateID,
			connectionID:       connectionID,
			mockError:          nil,
			expectedStatusCode: http.StatusNoContent,
		},
		{
			name:               "invalid template id",
			templateID:         "",
			connectionID:       connectionID,
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "invalid connection id",
			templateID:         templateID,
			connectionID:       "",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "update template error",
			templateID:         templateID,
			connectionID:       connectionID,
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockError != nil && tt.templateID != "" && tt.connectionID != "" {
				mockTemplateSrv.On("ModifyTemplate", context.Background(), mockTemplate).
					Return(tt.mockError).Once()
			}

			if tt.mockError == nil {
				mockTemplateSrv.On("ModifyTemplate", context.Background(), mockTemplate).
					Return(nil).Once()
			}

			var req *http.Request
			req, err = http.NewRequestWithContext(context.Background(), http.MethodPut, fmt.Sprintf("/api/v1/templates/%s/%s", tt.templateID, tt.connectionID), bytes.NewReader(body))
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				h.ModifyTemplate(w, r, tt.connectionID, tt.templateID)
			})
			handlerFunc.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatusCode, rr.Code)
			mockTemplateSrv.AssertExpectations(t)
		})
	}
}

func TestDeleteTemplate(t *testing.T) {
	mockTemplateSrv := new(apimock.TemplateServiceMock)
	h := NewHandler(mockTemplateSrv)

	templateID := uuid.NewString()
	connectioID := uuid.NewString()

	tests := []struct {
		name               string
		templateID         string
		connectionID       string
		mockError          error
		expectedStatusCode int
	}{
		{
			name:               "template delete successful",
			templateID:         templateID,
			connectionID:       connectioID,
			mockError:          nil,
			expectedStatusCode: http.StatusNoContent,
		},
		{
			name:               "invalid template id",
			templateID:         "",
			connectionID:       connectioID,
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "invalid connection id",
			templateID:         templateID,
			connectionID:       "",
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:               "error on template deletion",
			templateID:         templateID,
			connectionID:       connectioID,
			mockError:          assert.AnError,
			expectedStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockError != nil && tt.templateID != "" && tt.connectionID != "" {
				mockTemplateSrv.On("RemoveTemplate", context.Background(), tt.templateID, tt.connectionID).
					Return(tt.mockError).Once()
			}

			if tt.mockError == nil {
				mockTemplateSrv.On("RemoveTemplate", context.Background(), tt.templateID, tt.connectionID).
					Return(nil).Once()
			}

			req, err := http.NewRequestWithContext(context.Background(), http.MethodPut, fmt.Sprintf("/api/v1/templates/%s/%s", tt.templateID, tt.connectionID), http.NoBody)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handlerFunc := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				h.DeleteTemplate(w, r, tt.templateID, tt.connectionID)
			})
			handlerFunc.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatusCode, rr.Code)
			mockTemplateSrv.AssertExpectations(t)
		})
	}
}
