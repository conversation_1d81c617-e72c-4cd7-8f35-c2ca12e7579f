package mongo

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
)

const templateCollection = "templates"

type TemplateRepositorySuite struct {
	suite.Suite

	db     *mongo.Database
	mongoC *mongodb.MongoDBContainer
}

func (s *TemplateRepositorySuite) SetupSuite() {
	ctx := context.Background()
	mongodbContainer, err := mongodb.Run(ctx, "mongo:latest")
	s.Require().NoError(err, "failed to setup mongo container")

	endpoint, err := mongodbContainer.ConnectionString(ctx)
	s.Require().NoError(err, "failed to get connection string: %s", endpoint)

	mongoClient, err := mongo.Connect(options.Client().ApplyURI(endpoint))
	s.Require().NoError(err, "failed to connect to MongoDB: %s", err)

	s.db = mongoClient.Database("fe-ev")
	s.mongoC = mongodbContainer
}

func (s *TemplateRepositorySuite) TestGetAllTemplates() {
	ctx := context.Background()
	templateRepo := NewTemplateRepository(s.db.Collection(templateCollection))
	templateID := uuid.NewString()
	connectionID := uuid.NewString()

	testTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
		HeaderText: domain.Text{
			Font:  "arial",
			Style: "bold",
			Color: "black",
		},
	}

	s.Run("get all template - no template found", func() {
		templates, err := templateRepo.GetAllTemplates(ctx)
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().Nil(templates, "templates result should be nil")
		s.Len(templates, 0, "zero templates are expected")
	})

	s.Run("get all templates - success", func() {
		_, err := s.db.Collection(templateCollection).InsertOne(ctx, testTemplate)
		s.Require().NoError(err, "failed to insert test template")
		templates, err := templateRepo.GetAllTemplates(ctx)
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().NotNil(templates, "expected a template to be retrieved")
		s.Len(templates, 1, "1 template is expected")
		s.Equal(testTemplate.TemplateID, templates[0].TemplateID, "expected retrieved template ID to match")
		s.Equal(testTemplate.ConnectionID, templates[0].ConnectionID, "expected retrieved connection ID to match")
	})
}

func (s *TemplateRepositorySuite) TestGetTemplate() {
	ctx := context.Background()
	templateRepo := NewTemplateRepository(s.db.Collection(templateCollection))
	templateID := uuid.NewString()
	connectionID := uuid.NewString()

	testTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
		HeaderText: domain.Text{
			Font:  "arial",
			Style: "bold",
			Color: "black",
		},
	}

	s.Run("retrieve valid template", func() {
		_, err := s.db.Collection(templateCollection).InsertOne(ctx, testTemplate)
		s.Require().NoError(err, "failed to insert test template")
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, templateID)
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().NotNil(retrievedTemplate, "expected a template to be retrieved")
		s.Equal(testTemplate.TemplateID, retrievedTemplate.TemplateID, "expected retrieved template ID to match")
		s.Equal(testTemplate.ConnectionID, retrievedTemplate.ConnectionID, "expected retrieved connection ID to match")
	})

	s.Run("retrieve non-existence template", func() {
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, uuid.NewString())
		s.Require().Error(err, "expected error when retrieving non-existent template")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})

	s.Run("retrieve template with empty ID", func() {
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, "")
		s.Require().Error(err, "expected error when retrieving template with empty ID")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})

	s.Run("retrieve template with malformed ID", func() {
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, "invalid-template-id")
		s.Require().Error(err, "expected error when retrieving template with malformed ID")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})
}

func (s *TemplateRepositorySuite) TestGetTemplateByCountryAndParty() {
	ctx := context.Background()
	templateRepo := NewTemplateRepository(s.db.Collection(templateCollection))
	templateID := uuid.NewString()
	connectionID := uuid.NewString()

	testTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
		HeaderText: domain.Text{
			Font:  "arial",
			Style: "bold",
			Color: "black",
		},
		Connection: domain.Connection{
			ConnectionName: "Test Connection",
			CountryCode:    "US",
			PartyCode:      "ABC123",
			CPOURL:         "https://example.com",
		},
	}

	s.Run("retrieve template by country and party - case sensitive match", func() {
		_, err := s.db.Collection(templateCollection).InsertOne(ctx, testTemplate)
		s.Require().NoError(err, "failed to insert test template")

		retrievedTemplate, err := templateRepo.GetTemplateByCountryAndParty(ctx, "US", "ABC123")
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().NotNil(retrievedTemplate, "expected a template to be retrieved")
		s.Equal(testTemplate.TemplateID, retrievedTemplate.TemplateID, "expected retrieved template ID to match")
		s.Equal(testTemplate.Connection.CountryCode, retrievedTemplate.Connection.CountryCode, "expected retrieved country code to match")
		s.Equal(testTemplate.Connection.PartyCode, retrievedTemplate.Connection.PartyCode, "expected retrieved party code to match")
	})

	s.Run("retrieve template by country and party - case insensitive match", func() {
		retrievedTemplate, err := templateRepo.GetTemplateByCountryAndParty(ctx, "us", "abc123")
		s.Require().NoError(err, "expected no error when retrieving template with different case")
		s.Require().NotNil(retrievedTemplate, "expected a template to be retrieved")
		s.Equal(testTemplate.TemplateID, retrievedTemplate.TemplateID, "expected retrieved template ID to match")
		s.Equal(testTemplate.Connection.CountryCode, retrievedTemplate.Connection.CountryCode, "expected retrieved country code to match")
		s.Equal(testTemplate.Connection.PartyCode, retrievedTemplate.Connection.PartyCode, "expected retrieved party code to match")
	})

	s.Run("retrieve template by country and party - mixed case", func() {
		retrievedTemplate, err := templateRepo.GetTemplateByCountryAndParty(ctx, "Us", "AbC123")
		s.Require().NoError(err, "expected no error when retrieving template with mixed case")
		s.Require().NotNil(retrievedTemplate, "expected a template to be retrieved")
		s.Equal(testTemplate.TemplateID, retrievedTemplate.TemplateID, "expected retrieved template ID to match")
	})

	s.Run("retrieve non-existent template by country and party", func() {
		retrievedTemplate, err := templateRepo.GetTemplateByCountryAndParty(ctx, "XX", "NONEXISTENT")
		s.Require().Error(err, "expected error when retrieving non-existent template")
		s.Equal(domain.ErrRecordNotFound, err, "expected ErrRecordNotFound error")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})

	s.Run("retrieve template with empty country code", func() {
		retrievedTemplate, err := templateRepo.GetTemplateByCountryAndParty(ctx, "", "ABC123")
		s.Require().Error(err, "expected error when retrieving template with empty country code")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})

	s.Run("retrieve template with empty party code", func() {
		retrievedTemplate, err := templateRepo.GetTemplateByCountryAndParty(ctx, "US", "")
		s.Require().Error(err, "expected error when retrieving template with empty party code")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})
}

func TestTemplateRepositorySuite(t *testing.T) {
	suite.Run(t, new(TemplateRepositorySuite))
}
