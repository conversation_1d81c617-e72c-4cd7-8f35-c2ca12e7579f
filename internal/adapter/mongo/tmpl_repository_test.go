package mongo

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go/modules/mongodb"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"

	"inv-cloud-platform/fe-ev-rcpt-tmpl-api/internal/core/domain"
)

const templateCollection = "templates"

type TemplateRepositorySuite struct {
	suite.Suite

	db     *mongo.Database
	mongoC *mongodb.MongoDBContainer
}

func (s *TemplateRepositorySuite) SetupSuite() {
	ctx := context.Background()
	mongodbContainer, err := mongodb.Run(ctx, "mongo:latest")
	s.Require().NoError(err, "failed to setup mongo container")

	endpoint, err := mongodbContainer.ConnectionString(ctx)
	s.Require().NoError(err, "failed to get connection string: %s", endpoint)

	mongoClient, err := mongo.Connect(options.Client().ApplyURI(endpoint))
	s.Require().NoError(err, "failed to connect to MongoDB: %s", err)

	s.db = mongoClient.Database("fe-ev")
	s.mongoC = mongodbContainer
}

func (s *TemplateRepositorySuite) TestGetAllTemplates() {
	ctx := context.Background()
	templateRepo := NewTemplateRepository(s.db.Collection(templateCollection))
	templateID := uuid.NewString()
	connectionID := uuid.NewString()

	testTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
		HeaderText: domain.Text{
			Font:  "arial",
			Style: "bold",
			Color: "black",
		},
	}

	s.Run("get all template - no template found", func() {
		templates, err := templateRepo.GetAllTemplates(ctx)
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().Nil(templates, "templates result should be nil")
		s.Len(templates, 0, "zero templates are expected")
	})

	s.Run("get all templates - success", func() {
		_, err := s.db.Collection(templateCollection).InsertOne(ctx, testTemplate)
		s.Require().NoError(err, "failed to insert test template")
		templates, err := templateRepo.GetAllTemplates(ctx)
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().NotNil(templates, "expected a template to be retrieved")
		s.Len(templates, 1, "1 template is expected")
		s.Equal(testTemplate.TemplateID, templates[0].TemplateID, "expected retrieved template ID to match")
		s.Equal(testTemplate.ConnectionID, templates[0].ConnectionID, "expected retrieved connection ID to match")
	})
}

func (s *TemplateRepositorySuite) TestGetTemplate() {
	ctx := context.Background()
	templateRepo := NewTemplateRepository(s.db.Collection(templateCollection))
	templateID := uuid.NewString()
	connectionID := uuid.NewString()

	testTemplate := &domain.Template{
		TemplateID:   templateID,
		ConnectionID: connectionID,
		IsDefault:    true,
		Body:         "test-body",
		HeaderText: domain.Text{
			Font:  "arial",
			Style: "bold",
			Color: "black",
		},
	}

	s.Run("retrieve valid template", func() {
		_, err := s.db.Collection(templateCollection).InsertOne(ctx, testTemplate)
		s.Require().NoError(err, "failed to insert test template")
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, templateID)
		s.Require().NoError(err, "expected no error when retrieving template")
		s.Require().NotNil(retrievedTemplate, "expected a template to be retrieved")
		s.Equal(testTemplate.TemplateID, retrievedTemplate.TemplateID, "expected retrieved template ID to match")
		s.Equal(testTemplate.ConnectionID, retrievedTemplate.ConnectionID, "expected retrieved connection ID to match")
	})

	s.Run("retrieve non-existence template", func() {
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, uuid.NewString())
		s.Require().Error(err, "expected error when retrieving non-existent template")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})

	s.Run("retrieve template with empty ID", func() {
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, "")
		s.Require().Error(err, "expected error when retrieving template with empty ID")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})

	s.Run("retrieve template with malformed ID", func() {
		retrievedTemplate, err := templateRepo.GetTemplate(ctx, connectionID, "invalid-template-id")
		s.Require().Error(err, "expected error when retrieving template with malformed ID")
		s.Nil(retrievedTemplate, "expected retrieved template to be nil")
	})
}

func TestTemplateRepositorySuite(t *testing.T) {
	suite.Run(t, new(TemplateRepositorySuite))
}
