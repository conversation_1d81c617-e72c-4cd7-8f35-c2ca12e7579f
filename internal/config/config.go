package config

import (
	"time"

	"github.com/caarlos0/env/v11"
	"github.com/rs/zerolog/log"
)

type Configs struct {
	App     *App
	MongoDB *MongoDB
	HTTP    *HTTP
}

func Load() *Configs {
	cfg := &Configs{
		App:     &App{},
		HTTP:    &HTTP{},
		MongoDB: &MongoDB{},
	}

	if err := env.Parse(cfg); err != nil {
		log.Panic().Err(err).Msg("failed to load app config")
	}

	log.Debug().Msg("app configured successfully")
	return cfg
}

// MongoDB defines mongo database configuration
type MongoDB struct {
	Schema              string        `env:"MONGO_SCHEMA" envDefault:"mongodb"`
	Host                string        `env:"MONGO_HOST" envDefault:"localhost" `
	Port                int           `env:"MONGO_PORT" envDefault:"27017"`
	Username            string        `env:"MONGO_USERNAME" envDefault:"example"`
	Password            string        `env:"MONGO_PASSWORD" envDefault:"example"`
	AuthSource          string        `env:"MONGO_AUTH_SOURCE" envDefault:"admin"`
	Database            string        `env:"MONGO_DATABASE" envDefault:"fe-ev"`
	Options             string        `env:"MONGO_OPTIONS" envDefault:"authSource=admin"`
	Timeout             time.Duration `env:"MONGO_TIMEOUT" envDefault:"30s"`
	TemplatesCollection string        `env:"MONGO_RCPT_TMPL_COLLECTION" envDefault:"templates"`
}

type HTTP struct {
	Timeout  time.Duration `env:"HTTP_CLIENT_TIMEOUT"   envDefault:"30s"`
	RetryMax int           `env:"HTTP_CLIENT_RETRY_MAX" envDefault:"3"`
}

type App struct {
	LogLevel    string        `env:"LOG_LEVEL" envDefault:"debug"`
	ServerPort  int           `env:"SERVER_PORT" envDefault:"8080"`
	ReadTimeout time.Duration `env:"SERVER_READ_TIMEOUT" envDefault:"500s"`
	MonitorPort int           `env:"MONITOR_PORT" envDefault:"9090"`
}
