  #  FE-EV-RCPT-TMPL-API

Template API for generating transactional receipt

### Tooling
* golang v1.23
* docker 
* `make` - this project uses Makefiles

### Setup
To set up golang dependencies: `go mod download` and then `go mod tidy`

### Static Analysis Tools
The project uses [golangci-lint](https://golangci-lint.run/usage/quick-start/) lint tool to detect potential issues, bugs, and code style violations in Go codebases.

`make lint`

### Scripts
Many convenience commands are defined in the `Makefile`, run `make help` to learn more.

### Unit Tests
`make test`

### Docker Image
To create and push docker image to the image repo, one needs to create and push tag following the semantic versioning `release/v.a.b.c`
```
  git tag release/v1.0.0 && git tag push origin release/v1.0.0
```

### OpenAPI Generator docs
The app uses OpenAPI Generator to generate API docs. To generate docs, run:
`make codegen`

### Deployment
To deploy the app, one needs to use https://github.com/inv-cloud-platform/fe-deploy by following the steps listed in README.md.

